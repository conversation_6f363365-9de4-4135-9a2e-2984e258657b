<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>711</width>
    <height>869</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout_22">
    <item>
     <widget class="QTabWidget" name="tabs">
      <property name="currentIndex">
       <number>3</number>
      </property>
      <widget class="QWidget" name="tab_ExpInfo">
       <attribute name="title">
        <string>Exp Info</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QGroupBox" name="groupBox_fileIO">
            <property name="title">
             <string>File IO</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <item>
              <widget class="QComboBox" name="comboBox_tableOfExpInfoDB"/>
             </item>
             <item>
              <widget class="QPushButton" name="btn_openDB">
               <property name="text">
                <string>Open DB</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_saveToDB">
               <property name="text">
                <string>Save to DB</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="1">
           <widget class="QGroupBox" name="groupBox_animals">
            <property name="title">
             <string>Animals</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_12">
             <item>
              <layout class="QFormLayout" name="formLayout_2">
               <item row="0" column="0">
                <widget class="QLabel" name="label_3">
                 <property name="text">
                  <string>Animal ID</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="lineEdit_animalID"/>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_4">
                 <property name="text">
                  <string>Species</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QComboBox" name="comboBox_species"/>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_6">
                 <property name="text">
                  <string>Genotype</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QComboBox" name="comboBox_genotype"/>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="label_7">
                 <property name="text">
                  <string>Sex</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QComboBox" name="comboBox_sex"/>
               </item>
               <item row="4" column="0">
                <widget class="QLabel" name="label_8">
                 <property name="text">
                  <string>Date of Birth</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QDateEdit" name="dateEdit_DOB"/>
               </item>
               <item row="6" column="0">
                <widget class="QLabel" name="label_9">
                 <property name="text">
                  <string>Date of Injection</string>
                 </property>
                </widget>
               </item>
               <item row="6" column="1">
                <widget class="QDateEdit" name="dateEdit_DOI"/>
               </item>
               <item row="5" column="0">
                <widget class="QLabel" name="label_10">
                 <property name="text">
                  <string>Ages (weeks)</string>
                 </property>
                </widget>
               </item>
               <item row="5" column="1">
                <widget class="QLabel" name="lbl_ages">
                 <property name="text">
                  <string>0</string>
                 </property>
                </widget>
               </item>
               <item row="7" column="0">
                <widget class="QLabel" name="label_26">
                 <property name="text">
                  <string>Incubation (weeks)</string>
                 </property>
                </widget>
               </item>
               <item row="7" column="1">
                <widget class="QLabel" name="lbl_incubation">
                 <property name="text">
                  <string>0</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <item>
             <widget class="QGroupBox" name="groupBox_basics">
              <property name="enabled">
               <bool>true</bool>
              </property>
              <property name="title">
               <string>Basics</string>
              </property>
              <layout class="QFormLayout" name="formLayout_4">
               <item row="0" column="0">
                <widget class="QLabel" name="label">
                 <property name="text">
                  <string>Date of Recording</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QDateEdit" name="dateEdit_DOR">
                 <property name="calendarPopup">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_2">
                 <property name="text">
                  <string>Experimenters</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QLineEdit" name="lineEdit_experimenters"/>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_5">
                 <property name="text">
                  <string>ACUC protocol</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <layout class="QHBoxLayout" name="horizontalLayout">
                 <item>
                  <widget class="QComboBox" name="comboBox_ACUC"/>
                 </item>
                 <item>
                  <widget class="QPushButton" name="btn_add_ACUC_PN">
                   <property name="maximumSize">
                    <size>
                     <width>20</width>
                     <height>20</height>
                    </size>
                   </property>
                   <property name="text">
                    <string>+</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="btn_rm_ACUC_PN">
                   <property name="maximumSize">
                    <size>
                     <width>20</width>
                     <height>20</height>
                    </size>
                   </property>
                   <property name="text">
                    <string>-</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_solutions">
              <property name="title">
               <string>Solutions</string>
              </property>
              <layout class="QFormLayout" name="formLayout_8">
               <item row="0" column="0">
                <widget class="QLabel" name="label_30">
                 <property name="text">
                  <string>Cutting(mOsm/Kg)</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="lineEdit_CuttingOS"/>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_31">
                 <property name="text">
                  <string>Holding(mOsm/Kg)</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QLineEdit" name="lineEdit_HoldingOS"/>
               </item>
               <item row="4" column="0">
                <widget class="QLabel" name="label_32">
                 <property name="text">
                  <string>Recording(mOsm/Kg)</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QLineEdit" name="lineEdit_RecordingOS"/>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_injections">
          <property name="title">
           <string>Injections</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_15">
             <item>
              <widget class="QLabel" name="label_33">
               <property name="text">
                <string>Target Area</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="checkBox_enable_R">
               <property name="text">
                <string>Right</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="checkBox_enable_L">
               <property name="text">
                <string>Left</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_targetArea"/>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_virus_R">
             <property name="title">
              <string>Right Hemisphere</string>
             </property>
             <layout class="QFormLayout" name="formLayout_5">
              <item row="0" column="0">
               <widget class="QLabel" name="label_11">
                <property name="text">
                 <string>Volume</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_17">
                <property name="text">
                 <string>Mode</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="comboBox_injectionMode_R"/>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_12">
                <property name="text">
                 <string>Coordinates</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <layout class="QHBoxLayout" name="horizontalLayout_12">
                <item>
                 <widget class="QLabel" name="label_13">
                  <property name="text">
                   <string>DV</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_Coord_DV_R"/>
                </item>
                <item>
                 <widget class="QLabel" name="label_14">
                  <property name="text">
                   <string>ML</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_Coord_ML_R"/>
                </item>
                <item>
                 <widget class="QLabel" name="label_15">
                  <property name="text">
                   <string>AP</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_Coord_AP_R"/>
                </item>
               </layout>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label_16">
                <property name="text">
                 <string>Virus</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <layout class="QHBoxLayout" name="horizontalLayout_2">
                <item>
                 <widget class="QComboBox" name="comboBox_virus_R">
                  <property name="enabled">
                   <bool>true</bool>
                  </property>
                  <property name="font">
                   <font>
                    <kerning>true</kerning>
                   </font>
                  </property>
                  <property name="editable">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_add_virus_R">
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>+</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_rm_virus_R">
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>-</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="0" column="1">
               <layout class="QHBoxLayout" name="horizontalLayout_7">
                <item>
                 <widget class="QLineEdit" name="lineEdit_volume_R"/>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBox_volumeUnit_R"/>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_virus_L">
             <property name="title">
              <string>Left Hemisphere</string>
             </property>
             <layout class="QFormLayout" name="formLayout_3">
              <item row="0" column="0">
               <widget class="QLabel" name="label_18">
                <property name="text">
                 <string>Volume</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_24">
                <property name="text">
                 <string>Mode</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_19">
                <property name="text">
                 <string>Coordinates</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label_23">
                <property name="text">
                 <string>Virus</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="comboBox_injectionMode_L"/>
              </item>
              <item row="2" column="1">
               <layout class="QHBoxLayout" name="horizontalLayout_13">
                <item>
                 <widget class="QLabel" name="label_20">
                  <property name="text">
                   <string>DV</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_Coord_DV_L"/>
                </item>
                <item>
                 <widget class="QLabel" name="label_21">
                  <property name="text">
                   <string>ML</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_Coord_ML_L"/>
                </item>
                <item>
                 <widget class="QLabel" name="label_22">
                  <property name="text">
                   <string>AP</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_Coord_AP_L"/>
                </item>
               </layout>
              </item>
              <item row="3" column="1">
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <item>
                 <widget class="QComboBox" name="comboBox_virus_L"/>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_add_virus_L">
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>+</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_rm_virus_L">
                  <property name="maximumSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>-</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item row="0" column="1">
               <layout class="QHBoxLayout" name="horizontalLayout_9">
                <item>
                 <widget class="QLineEdit" name="lineEdit_volume_L"/>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBox_volumeUnit_L"/>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_RecTagger">
       <attribute name="title">
        <string>Rec Tagger</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_23">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <item>
           <widget class="QGroupBox" name="groupBox_recBasic">
            <property name="title">
             <string>Experiment Date: YYYY_MM_DD</string>
            </property>
            <layout class="QFormLayout" name="formLayout_6">
             <item row="0" column="0">
              <widget class="QLabel" name="lbl_OBJ">
               <property name="text">
                <string>OBJ</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <layout class="QHBoxLayout" name="horizontalLayout_16">
               <item>
                <widget class="QRadioButton" name="radioBtn_10X">
                 <property name="text">
                  <string>10X</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="radioBtn_40X">
                 <property name="text">
                  <string>40X</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="radioBtn_60X">
                 <property name="text">
                  <string>60X</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="lbl_EXC">
               <property name="text">
                <string>EXC</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QComboBox" name="comboBox_EXC"/>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="lbl_LEVEL">
               <property name="text">
                <string>LEVEL</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLineEdit" name="lineEdit_LEVEL"/>
             </item>
             <item row="3" column="0">
              <widget class="QLabel" name="lbl_EXPO">
               <property name="text">
                <string>EXPO</string>
               </property>
              </widget>
             </item>
             <item row="3" column="1">
              <layout class="QHBoxLayout" name="horizontalLayout_17">
               <item>
                <widget class="QLineEdit" name="lineEdit_EXPO"/>
               </item>
               <item>
                <widget class="QComboBox" name="comboBox_EXPO_UNITS"/>
               </item>
              </layout>
             </item>
             <item row="4" column="0">
              <widget class="QLabel" name="lbl_EMI">
               <property name="text">
                <string>EMI</string>
               </property>
              </widget>
             </item>
             <item row="4" column="1">
              <widget class="QComboBox" name="comboBox_EMI"/>
             </item>
             <item row="5" column="0">
              <widget class="QLabel" name="lbl_FRAMES">
               <property name="text">
                <string>FRAMES (p)</string>
               </property>
              </widget>
             </item>
             <item row="5" column="1">
              <widget class="QLineEdit" name="lineEdit_FRAMES"/>
             </item>
             <item row="6" column="0">
              <widget class="QLabel" name="lbl_FPS">
               <property name="text">
                <string>FPS (Hz)</string>
               </property>
              </widget>
             </item>
             <item row="6" column="1">
              <widget class="QLineEdit" name="lineEdit_FPS"/>
             </item>
             <item row="7" column="0">
              <widget class="QLabel" name="lbl_CAM_TRIG_MODE">
               <property name="text">
                <string>CAM_TRIG_MODE</string>
               </property>
              </widget>
             </item>
             <item row="7" column="1">
              <widget class="QComboBox" name="comboBox_CAM_TRIG_MODES"/>
             </item>
             <item row="8" column="0">
              <widget class="QLabel" name="lbl_SLICE">
               <property name="text">
                <string>SLICE</string>
               </property>
              </widget>
             </item>
             <item row="8" column="1">
              <widget class="QSpinBox" name="spinBox_SLICE"/>
             </item>
             <item row="9" column="0">
              <widget class="QLabel" name="lbl_AT">
               <property name="text">
                <string>AT</string>
               </property>
              </widget>
             </item>
             <item row="9" column="1">
              <layout class="QHBoxLayout" name="horizontalLayout_20">
               <item>
                <widget class="QComboBox" name="comboBox_LOC_TYPES"/>
               </item>
               <item>
                <widget class="QSpinBox" name="spinBox_AT"/>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_recCustomized">
            <property name="title">
             <string>Customized Parameters</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_7">
             <item>
              <widget class="QCheckBox" name="checkBox_addCustomized">
               <property name="text">
                <string>Check to add customized parameters</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_21">
               <item>
                <widget class="QComboBox" name="comboBox_tagTemplates"/>
               </item>
               <item>
                <widget class="QPushButton" name="btn_saveTemplate">
                 <property name="text">
                  <string>Save</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btn_deleteCurrentTemplate">
                 <property name="text">
                  <string>Delete</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QTableView" name="tableView_customized">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="autoFillBackground">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_8">
               <item>
                <widget class="QPushButton" name="btn_removeSelectedRows">
                 <property name="text">
                  <string>Remove</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btn_addNewRows">
                 <property name="text">
                  <string>Insert</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btn_moveUp">
                 <property name="text">
                  <string>Shift Up</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btn_moveDown">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="text">
                  <string>Shift Down</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_13">
          <item>
           <widget class="QGroupBox" name="groupBox_status">
            <property name="title">
             <string>Status</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_16">
             <item>
              <widget class="QTextBrowser" name="textBrowser_status"/>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_tagOutput">
            <property name="title">
             <string>Output</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_11">
             <item>
              <widget class="QLabel" name="lbl_recDir">
               <property name="text">
                <string>Directory of the recording files</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_11">
               <item>
                <widget class="QLineEdit" name="lineEdit_recDir"/>
               </item>
               <item>
                <widget class="QPushButton" name="btn_browse">
                 <property name="text">
                  <string>Browse</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QLabel" name="lbl_serialName_2">
               <property name="text">
                <string>Filename SN</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_18">
               <item>
                <widget class="QLineEdit" name="lineEdit_filenameSN">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="text">
                  <string/>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignCenter</set>
                 </property>
                 <property name="placeholderText">
                  <string/>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QGridLayout" name="gridLayout">
                 <item row="1" column="0">
                  <widget class="QPushButton" name="btn_decreaseSN">
                   <property name="text">
                    <string>-</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <widget class="QPushButton" name="btn_increaseSN">
                   <property name="text">
                    <string>+</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QPushButton" name="btn_resetSN">
                   <property name="text">
                    <string>Reset</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="1">
                  <widget class="QPushButton" name="btn_copyFilenameSN">
                   <property name="text">
                    <string>Copy</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_tagPreview">
               <property name="title">
                <string>Tag Preview and Operations</string>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_14">
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_19">
                  <item>
                   <widget class="QPushButton" name="btn_writeToRec">
                    <property name="text">
                     <string>Write</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btn_loadFromRec">
                    <property name="text">
                     <string>Load</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btn_recoverRec">
                    <property name="text">
                     <string>Recover</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <widget class="QTextEdit" name="textEdit_tags"/>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_RecSumGen">
       <attribute name="title">
        <string>Rec Database</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_21">
        <item>
         <widget class="QGroupBox" name="groupBox_recDB_status">
          <property name="title">
           <string>Status</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_6">
           <item>
            <widget class="QTextBrowser" name="textBrowser_recDB"/>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox">
          <property name="title">
           <string>Database Operation</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_8">
           <item>
            <widget class="QPushButton" name="btn_importRecDB">
             <property name="text">
              <string>Import Data From Rec Files</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_14">
             <item>
              <widget class="QLabel" name="label_25">
               <property name="text">
                <string>Table Name</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QComboBox" name="comboBox_tableOfRecDB"/>
             </item>
             <item>
              <widget class="QPushButton" name="btn_loadRecDB">
               <property name="text">
                <string>Load</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_exportSummary">
               <property name="text">
                <string>Export</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_deleteTable">
               <property name="text">
                <string>Delete</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_2">
          <property name="title">
           <string>Display</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_9">
           <item>
            <widget class="QTableView" name="tableView_recDB"/>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_Concatenater">
       <attribute name="title">
        <string>Concatenator (Tiff)</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_10">
        <item>
         <widget class="QGroupBox" name="groupBox_concat_status">
          <property name="title">
           <string>Status</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_15">
           <item>
            <widget class="QTextBrowser" name="textBrowser_concatenator"/>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_tiffBrowser">
          <property name="title">
           <string>File Browser (Tiff)</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_17">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_10">
             <item>
              <widget class="QCheckBox" name="checkBox_includeSubfolders">
               <property name="text">
                <string>Include subfolders</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_browse_tiffs">
               <property name="text">
                <string>Browse</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QCheckBox" name="checkBox_selectAllFiles">
             <property name="text">
              <string>All</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QListView" name="listView_recFiles"/>
           </item>
           <item>
            <widget class="QPushButton" name="btn_start_concatenation">
             <property name="text">
              <string>Start</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QProgressBar" name="progressBar_concatenation">
          <property name="value">
           <number>0</number>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>Quick Analysis</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_22">
        <item>
         <widget class="QGroupBox" name="groupBox_10">
          <property name="title">
           <string>Preprocessing Parameters</string>
          </property>
          <layout class="QFormLayout" name="formLayout_7">
           <item row="0" column="0">
            <widget class="QLabel" name="label_45">
             <property name="text">
              <string>Directory</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <widget class="QLineEdit" name="lineEdit_20"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_8">
               <property name="text">
                <string>Browse</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_46">
             <property name="text">
              <string>Filename</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_44">
             <property name="text">
              <string>Gaussian Kernel Radius (pixels)</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <layout class="QHBoxLayout" name="horizontalLayout_25">
             <item>
              <widget class="QComboBox" name="comboBox_16"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_10">
               <property name="text">
                <string>Process</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="1" column="1">
            <layout class="QHBoxLayout" name="horizontalLayout_26">
             <item>
              <widget class="QComboBox" name="comboBox_15"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_4">
               <property name="text">
                <string>Load</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_11">
          <property name="title">
           <string>Release Map</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <widget class="QGraphicsView" name="graphicsView"/>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>711</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
