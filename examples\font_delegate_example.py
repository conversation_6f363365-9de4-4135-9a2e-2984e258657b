"""
Example demonstrating how to use custom delegates for font styling in QTableView
"""

import sys
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QTableView, QVBoxLayout, 
    QWidget, QPushButton, QHBoxLayout
)
from PySide6.QtCore import QAbstractTableModel, Qt
from PySide6.QtGui import QFont

# Import your custom delegates
from classes.customized_delegate import (
    FontStyledDelegate, 
    ConditionalFontDelegate, 
    CenterAlignDelegate,
    CellEditDelegate
)


class SampleTableModel(QAbstractTableModel):
    """Sample model with different types of data for testing font delegates"""
    
    def __init__(self):
        super().__init__()
        self.data_list = [
            ["Header 1", "Normal Text", "Code123", 150],
            ["Header 2", "WARNING: Check this", "Function()", -50],
            ["Header 3", "ERROR: Failed", "Variable", 75],
            ["Header 4", "Regular text", "Class.method", 200],
            ["Header 5", "Some info", "array[0]", 25]
        ]
        self.headers = ["Type", "Message", "Code", "Value"]
    
    def rowCount(self, parent=None):
        return len(self.data_list)
    
    def columnCount(self, parent=None):
        return len(self.headers)
    
    def data(self, index, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            return self.data_list[index.row()][index.column()]
        return None
    
    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return None
    
    def flags(self, index):
        return Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsEditable


class FontDelegateExample(QMainWindow):
    """Main window demonstrating different font delegates"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Font Delegate Examples")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create table view
        self.table_view = QTableView()
        self.model = SampleTableModel()
        self.table_view.setModel(self.model)
        
        # Set initial delegate
        self.current_delegate = FontStyledDelegate()
        self.table_view.setItemDelegate(self.current_delegate)
        
        layout.addWidget(self.table_view)
        
        # Create buttons to switch between different delegates
        button_layout = QHBoxLayout()
        
        font_styled_btn = QPushButton("Font Styled Delegate")
        font_styled_btn.clicked.connect(self.use_font_styled_delegate)
        button_layout.addWidget(font_styled_btn)
        
        conditional_btn = QPushButton("Conditional Font Delegate")
        conditional_btn.clicked.connect(self.use_conditional_delegate)
        button_layout.addWidget(conditional_btn)
        
        center_align_btn = QPushButton("Center Align Delegate")
        center_align_btn.clicked.connect(self.use_center_align_delegate)
        button_layout.addWidget(center_align_btn)
        
        edit_delegate_btn = QPushButton("Cell Edit Delegate")
        edit_delegate_btn.clicked.connect(self.use_edit_delegate)
        button_layout.addWidget(edit_delegate_btn)
        
        layout.addLayout(button_layout)
        
        # Resize columns to content
        self.table_view.resizeColumnsToContents()
    
    def use_font_styled_delegate(self):
        """Switch to FontStyledDelegate"""
        self.table_view.setItemDelegate(FontStyledDelegate())
        self.table_view.viewport().update()
    
    def use_conditional_delegate(self):
        """Switch to ConditionalFontDelegate"""
        self.table_view.setItemDelegate(ConditionalFontDelegate())
        self.table_view.viewport().update()
    
    def use_center_align_delegate(self):
        """Switch to CenterAlignDelegate"""
        self.table_view.setItemDelegate(CenterAlignDelegate())
        self.table_view.viewport().update()
    
    def use_edit_delegate(self):
        """Switch to CellEditDelegate"""
        self.table_view.setItemDelegate(CellEditDelegate())
        self.table_view.viewport().update()


def main():
    app = QApplication(sys.argv)
    
    # Set application font
    app.setFont(QFont("Segoe UI", 9))
    
    window = FontDelegateExample()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
