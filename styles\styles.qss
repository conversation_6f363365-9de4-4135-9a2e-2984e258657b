QDateEdit {
    font-size: 14px;
    color: blue;
}

QDateEdit::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 24px; /* Adjust the width of the drop-down button */
    border: none;
    background: transparent;
}

QDateEdit::down-arrow {
    image: url(:icons/calendar-select.png); /* Path to your custom image */
    width: 20px; /* Adjust the width of the image */
    height: 20px; /* Adjust the height of the image */
}

QGroupBox {
    font-size: 18px;
    font-weight: bold;
}

QLabel {
    font-size: 14px;
    font-weight: bold;
}

QRadioButton {
    font-size: 14px;
}

QComboBox{
    font-size: 14px;
}

QLineEdit {
    font-size: 14px;
}

QSpinBox {
    font-size: 14px;
}

QHeaderView::section {
    font-family: "Hack Nerd Font Mono";
    font-weight: bold;
    font-size: 14px;
    color:blue;
}

QTableView {
    font-size: 14px;
    font-weight: bold;
}

QScrollBar:vertical {
    width: 30px;
}

QScrollBar:horizontal {
    height: 30px;
}


QPushButton {
    font-size: 12px;
    font-weight: bold;
}

QPushButton#btn_deleteCurrentTemplate {
    color: red;
}

QPushButton#btn_deleteCurrentTemplate:disabled{
    color: gray;
}


QLineEdit#lineEdit_filenameSN {
    font-size: 16px;
    font-weight: bold;
    font-style: italic;
    color: green;
}

QPushButton#btn_copyFilenameSN:enabled {
    color: green;
}

QPushButton#btn_copyFilenameSN:disabled {
    color: gray;
}

QTextBrowser {
    font-family: "Hack Nerd Font Mono";
    font-size: 14px;
    font-weight: bold;
    background-color: black;
}

QTextEdit#textEdit_tags {
    font-size: 14px;
    font-weight: bold;
    color: purple;
}

QProgressBar {
    border-style: solid;
    border-color: gray;
    border-radius: 7px;
    border-width: 2px;
    background-color: darkgray;
    text-align: center;
    font-family: "Hack Nerd Font Mono";
    font-size: 18px;
    color: #f9f9f9;
    font-weight: bold;
    padding: 5px;
}
QProgressBar::chunk {
    background-color: darkviolet;
}

QCheckBox {
    font-size: 14px;
    font-weight: bold;
}

QCheckBox::indicator
 {
    width: 40px;
    height: 40px;
}

QCheckBox::indicator:unchecked {
    image: url(:checkbox/unchecked.svg);
}

QCheckBox::indicator:checked {
    image: url(:checkbox/checked.svg);
}

QListView::indicator {
    width: 40px;
    height: 40px;
    margin-right:100px
}

QListView::indicator:unchecked {
    image: url(:checkbox/unchecked.svg);
}

QListView::indicator:checked {
    image: url(:checkbox/checked.svg);
}
