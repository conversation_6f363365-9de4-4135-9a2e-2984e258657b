# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 6.8.1
# WARNING! All changes made in this file will be lost!

from PySide6 import QtCore

qt_resource_data = b"\
\x00\x00\x04\xcd\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg width\
=\x22800px\x22 height=\
\x22800px\x22 viewBox=\
\x220 0 24 24\x22 vers\
ion=\x221.1\x22 xmlns=\
\x22http://www.w3.o\
rg/2000/svg\x22 xml\
ns:xlink=\x22http:/\
/www.w3.org/1999\
/xlink\x22>\x0a    <!-\
- Uploaded to: S\
VG Repo, www.svg\
repo.com, Genera\
tor: SVG Repo Mi\
xer Tools -->\x0a  \
  <title>ic_flue\
nt_checkbox_chec\
ked_24_filled</t\
itle>\x0a    <desc>\
Created with Ske\
tch.</desc>\x0a    \
<g id=\x22\xf0\x9f\x94\x8d-Prod\
uct-Icons\x22 strok\
e=\x22none\x22 stroke-\
width=\x221\x22 fill=\x22\
none\x22 fill-rule=\
\x22evenodd\x22>\x0a     \
   <g id=\x22ic_flu\
ent_checkbox_che\
cked_24_filled\x22 \
fill=\x22#212121\x22 f\
ill-rule=\x22nonzer\
o\x22>\x0a            \
<path d=\x22M18,3 C\
19.6568542,3 21,\
4.34314575 21,6 \
L21,18 C21,19.65\
68542 19.6568542\
,21 18,21 L6,21 \
C4.34314575,21 3\
,19.6568542 3,18\
 L3,6 C3,4.34314\
575 4.34314575,3\
 6,3 L18,3 Z M16\
.4696699,7.96966\
991 L10,14.43933\
98 L7.53033009,1\
1.9696699 C7.237\
43687,11.6767767\
 6.76256313,11.6\
767767 6.4696699\
1,11.9696699 C6.\
1767767,12.26256\
31 6.1767767,12.\
7374369 6.469669\
91,13.0303301 L9\
.46966991,16.030\
3301 C9.76256313\
,16.3232233 10.2\
374369,16.323223\
3 10.5303301,16.\
0303301 L17.5303\
301,9.03033009 C\
17.8232233,8.737\
43687 17.8232233\
,8.26256313 17.5\
303301,7.9696699\
1 C17.2374369,7.\
6767767 16.76256\
31,7.6767767 16.\
4696699,7.969669\
91 Z\x22 id=\x22\xf0\x9f\x8e\xa8-C\
olor\x22>\x0a\x0d</path>\x0a\
        </g>\x0a   \
 </g>\x0a</svg>\
\x00\x00\x03\xba\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg width\
=\x22800px\x22 height=\
\x22800px\x22 viewBox=\
\x220 0 24 24\x22 vers\
ion=\x221.1\x22 xmlns=\
\x22http://www.w3.o\
rg/2000/svg\x22 xml\
ns:xlink=\x22http:/\
/www.w3.org/1999\
/xlink\x22>\x0a    <!-\
- Uploaded to: S\
VG Repo, www.svg\
repo.com, Genera\
tor: SVG Repo Mi\
xer Tools -->\x0a  \
  <title>ic_flue\
nt_checkbox_unch\
ecked_24_filled<\
/title>\x0a    <des\
c>Created with S\
ketch.</desc>\x0a  \
  <g id=\x22\xf0\x9f\x94\x8d-Pr\
oduct-Icons\x22 str\
oke=\x22none\x22 strok\
e-width=\x221\x22 fill\
=\x22none\x22 fill-rul\
e=\x22evenodd\x22>\x0a   \
     <g id=\x22ic_f\
luent_checkbox_u\
nchecked_24_fill\
ed\x22 fill=\x22#21212\
1\x22 fill-rule=\x22no\
nzero\x22>\x0a        \
    <path d=\x22M6,\
3 L18,3 C19.6568\
542,3 21,4.34314\
575 21,6 L21,18 \
C21,19.6568542 1\
9.6568542,21 18,\
21 L6,21 C4.3431\
4575,21 3,19.656\
8542 3,18 L3,6 C\
3,4.34314575 4.3\
4314575,3 6,3 Z \
M6,5 C5.44771525\
,5 5,5.44771525 \
5,6 L5,18 C5,18.\
5522847 5.447715\
25,19 6,19 L18,1\
9 C18.5522847,19\
 19,18.5522847 1\
9,18 L19,6 C19,5\
.44771525 18.552\
2847,5 18,5 L6,5\
 Z\x22 id=\x22\xf0\x9f\x8e\xa8Colo\
r\x22>\x0a\x0d</path>\x0a   \
     </g>\x0a    </\
g>\x0a</svg>\
\x00\x00\x02\x97\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x029IDATx\xda\xa4\
SKh\x13Q\x14\xbd3\xf9\x12\xf2\x05S\x9b\xd1\x12\
\xed\xc2.\x22\x85\xa4J\x10AH\x17Rp\xa7\xa0\x22\
\x88+\xdb\x85V\x17v\xe9gSD\x11A\x97\x8a\x82\
.]\x88\x1b[wu\xa7\xd0F\xa5]\xa4\xb1\x91\xd6\
J#%\xa6\xea\x0c:\xff\xe4yf\xe6\xa5\xb4`W\
yp\xe7\xdd\xf7\xee\xb9\xe7\xdd\xdf\x08\x8c1\xeaf\x89\
\xd4\xe5\xf2;\x9f\x17\x82@\x02\xd1\x08\xd4I~\xff\xbe\
M4nA\xd19(\xe8\xed9\xe0NA-@\xa6\
O3\xf6\xd8%\xb0=\xa7\xc9\xb3\x13\x13C\xba\xa6\xb1\
\xcfss\x99\xc5\xd9\xd9#H\xeeP\xe7%\xe8w)\
\x18<n\x99\xe6O\x90\xdc\xc7\xd5\x1b\xe7^pj\xf0\
\x0c\x11`\x8d\x024\x9a+\x163\xfbs9\xc9PU\
\xab<3\xf3\xe5G\xa3q>\x11\x8f_NK\xd2\xb1\
\xaf\xd5\xea\x22\x90\x97\x02D\xab\x8e\xc79\xf8n\x128\
\xa54\x89\x86\x10\xfa\x19\xa8\xc3\xc5R\xa9?\x9aL&\
\x96+\x95\xef\xa1p\x98\xaa\xf3\xf3\x1f\x11\xee\x18\x9c\xd7\
\x03\xce\xcb;\x10\xb89\xb7@\x04\xc0\xa3\xc1|~\x80\
D1T\xabT\x964M;(x\xa9\xb8k7'\
pk\xb0\xba\xbd-i\x5c\xde\xe8\x97\xa4=\xba,\x07\
\x97\xea\xf5j*\x16K\xa8\xb6\xbd\xf0\xc7\xb2\x06M^\
\xb3\xc6\xd66\xea\x5c,\xcfx}_:}\xb8\xc7\xe7\
\xeb\xa9\xd4\xeb5\xd90\x1e\xae4\x9b\x1fB\x81@\x04\
\xb6\xe9\xf6\xff\xe6\xa0C\x00\xf6\xb1\xdeX\xec\xc4\x01Q\
\x94\xd6\x14e}\xc30\x9e#\xe4\xa7p<\xf9MU\
\xa7\xa2~\x7f\x16\xfam\x8b\xa7\xbbI`{yg\x0d\
\xa2\x8bG\xc3\xe1\xacf\x9a\x7f\xcb\xb2\xfc\x09\xe7'\x0a\
lm\x0fsu\xcd\xb6_\xe2\xa1\x02\x9cK\xdb\x08\x9c\
\x03\xc0\xe3\xf9H$\x13m\xb5\xfcS\x8a\xb2\x82\xf3-\
\xd4\xe6\xd7\x03\xd8\xee@\xdey$7AP\x86mD\
\xe7CHN\x17z\xb1_\x80\xa1\x96J\xb5_E\xa3\
M\xe8\xf72;\x8cn\xc1\xc3^\xc3\xd8\x16X\xa7\x8d\
\x82 \xec\x85ax\x80\xe8\x0a\xc2\xfd\xfd\x16y\xa2\xca\
\xcb\xc0\xaa^pn\x16>\x883\x02\xc1>\xa2\xbe$\
\xd1\xae\x05\xc6^w\x08\xe2|\xdc\x03\xfc\xa1\x16\xcf\xcc\
\xe2:\xe3\xb3\xe3\xdb\x22\x22|7\x84n\x7f\xe7\x7f\x02\
\x0c\x00tp\xf0\x81(x-~\x00\x00\x00\x00IE\
ND\xaeB`\x82\
\x00\x00\x00\xe0\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x00\x82IDATx\xdab\
\xfc\xff\xff?\x03%\x80q\xd4\x00*\x19\xb0\x82\x91q\
&#\x03\x831)\x1a\x81\xd6\x9e\x8d\xf8\xff?\x9d\x05\
\xc4\xf9\x0e\xd4\x1c\x90\x96f\xfc\xf5\xebW\x86_\xbf~\
\x81\xf1\xef\xdf\xbf\xc1\xf8\xdf\xbf\x7f`\x0c\xb2\x08\x86\x99\
\x98\x98\x18\xae\x1d8\x006\x08l\xc0G \xfe\xf9\xf3\
'\xc3\xf7\xef\xdfQ\x0c\xf8\xfb\xf7/\x8aF\x18fd\
d\x04\xeb\x81\x1b\xf0\x1e\xe8\x9c\x89\x0b\x17\x92\xe4w6\
\xa0\x9e\xd1t0X\x0c\x00\x080\x00\x9a\xa3v\x1c%\
\xcf\x22\xd4\x00\x00\x00\x00IEND\xaeB`\x82\
\x00\x00\x03\x00\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x02\xa2IDATx\xda\x94\
S]H\x93a\x14~\xb6\xef\xdb\x9a)\xa3e\x81\x08\
\xb3\xacL3\x9c\x89\x9aN1\x179[\xa2\xfd\xd0\x95\
\x10]V\xe0@\x22$\xd0\x94\xbc\xaa\x9bn\xec&\xeb\
\xc2\x92\x91\x10\x99?E\xfe,\x94\x9c^53\xca4\
\xa5\x1f\xcd_\x9a\xb9$\xe7\xe6\xb6oo\xef\xf9r\x82\
A\x7f\x0f\x1c\xce\xfb\xbe\xdfs\x9e\xf7|\xe7\x9cWa\
j\xf8\x02Bu_\xac\xec\x15\x0a\x1cU\x00f\xfc\x05\
\x0c\xb0\xfb\x83\xe8\x12\xc3\x07\x8d\x86f\x94\x0e\x9d\xa6e\
qA\xe5=\xeb\xe4\xe8(\xdc\x8b\x8bH\xcf\xcf\x87\x14\
\x0c\xc253\x03A\x14\xa1R\xa9\xa0R\xab\xd1_W\
\xb6\x891\x88\xc2\xce\x93\x15\xb2\xc0\x92F\x8f\xc9-9\
\xd8\xec\x99R\xcdv\xd6y\xd51\xc9ii\xb9\xb9\xe8\
iiA|b\x22\xfc\x1e\x0f\x5c\xd3\xd3\x08\xf8|\x90\
\x02\x01\xc4eZ\x0e~xa\xf7Q\x06G\xb8\x95s\
\x8b\x9d\xd2\x19\x01\xfd\x05ma\xcc\x8e\x98\x94\x8c\x0c\xf4\
\xb6\xb5!3/O\xf6y\x16\x0b\x82<\xf0\xbb\xdb\x8d\
\xc0\xea*\xbe\xcdM\xb8<~\x16)\x86$\xa9<+\
9:\x7fk\xa4\xa8\x15\xc6:\x10\x8f.\x14\x99\x8d\xb8\
}\xc7\x86\xd2S\x16t\xb7\xb7\xe3\xb0\xd9\x0cGg'\
\x0epQ\x7f\x88\xe1\xed\x9bw\xab\x9f\xfa\x1f\x0e\x0bJ\
<Ur\x81\xd8\xa5\x85e\xed\xc7\x097|\x1d\xd7P\
\x98\x93\x86\x86\xa6v\xbcW\xef\xc7\x8d[6\x1c2\x99\
\xd0k\xb7#%5\x15\xdd=\xfdh\xb2\xbfB\xfdx\
\xb4k%\xc0\xceH!4\x82w\xc1\xc9\xd60>\xd0\
\xca\xeeZ\xb3eO\x18\xe9kf\xf5\xe7\xd2\xd9\xec\x93\
Z\xf6\xe0r\x01{\xd6t\x939\x9dNF1\xd4\xbd\
\xf3\x97* R\x85C\xa1\x108\x1f\xbb\xb2\x8ae#\
H\x92\x84\xbd9'\xe4o\x8f\xef_G\xbc\xe9,\xb4\
\xbb\xb3d\x1e\xc5\x10F\x92.\xfe\x14 \xb2\xb15\x09\
\x03\xc7G\xe0p8\xa0\xd3\xe9\xa0\xd1h\xa0\xe6\xed\xda\
\x93]\x22[\x18J\xa5\x12\x92s~}/ReI\
\xe0y\xd1k\x04\xb9\x98\xd1h\xdc00\xc1\xb5\xdb\xc2\
\x10\x04A\xee\xc6\x06\x01\x22\x0d\xd5T!\xa5\xa6\x16\x83\
\x83\x83\x88\x88\x88X'x\xbd^y\x1f\xf6\x06\x83\xe1\
\x17\x01\xbf_\xce`_e\xb5,\x94\xca\xab\xfd'\x10\
\x97b\xd6\x05h(\xe8\xb0\xea\xd16\x5c-\x99\xc3(\
\x8d0\x1f\x16\xaa\x03!\xbc\x8e\x8a\x8a\x82^\xaf\x97k\
@1\x84\x95\xc9\x97\x5c`-\x83+\xc7\xa6@\xbf\x9b\
\x90\x90\xf0\xdb\xdb)C\x91\xbf\x07\x8a\xf1}\x1e\xc2\xb4\
\xcd\x0a\xd1\xbb\xbc\xcc_\xa0B~$\xff\x02\xe2R\xcc\
\x8c\xcd\xba\x9dJ$~\x1d~>\x1cW6\x8f\xff\x01\
[\x18\x1b\xe7\x8e\x04\x16\x7f\x080\x00A\x00Z5\xf6\
\x03<I\x00\x00\x00\x00IEND\xaeB`\x82\
\x00\x00\x02\x13\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x01\xb5IDATx\xda\xa4\
S=K\x03A\x10\x9dl\xf6$\x86Dcg0\xa6\
\x09\x01\xcb\x80\x85\x85\x82`'\x88\x904V\x16\x92\xc6\
:\x85X\xe6'\xf8\x07DD\xb4L\x95: j!\
\x12\x05A\x10$\x84\xa0p\xd8\x1d\xe4\xc8\x85\xcd\xdd\xad\
3s\x09F0\x1f\xe2\xc2\xdc\xdc\xed\xbc\xf7\xe6\xed\xc7\
\x85\xb4\xd6\xf0\x9f!\x8b\xf2\x04S\xe8\x10\x1f\x99\xe1B\
\xbbp\xf7\xf6\x1b!^Y\xcfb\xba\xd2\xa0\x9f\x004\
\xc8\xa0\xbf\xce\x1e_\x14K\xadV\x0b\x92\xc9$\x98\xa6\
\x09\xe9\xfc6\xf4z=\xae\x9a\xd5\x00\x95\xce\xcf\xc0\xc3\
\xc2\xe7G\xed\xb4.\xa8+\xc6\xa3\xd0\xe0\xa3\x8e/\x94\
R\xe08\x0eX\x96\xc5\xd9\xb6\xbb\xf8ns\xd0\xf7`\
nn1\x92::?(!g_\x07\x0eX]\xf8\
\xbe\x8f\x00\x1b\x22\x91\x08\xe7N'\x84$\xc5\x9dm\xdb\
\xe5LsT\xa3f\xc8\x13\xbc\x07\xe4\x00G\xd8u]\
h\xb7\xdb\x10\x8dF9+\x15\x03\xad%\x13\x97vc\
\x9c\x95\x0a0\x84E^\x98\x05|\xed\xb1\x80\xe7y\xd8\
\xa1\x03\xcdf\x93\xc1\xcfg\x8d\x91;OX\xe4\xf5\x05\
\xfa\x0eh2\x97\xcbMut,0p0\xbc\x84\xdc\
^\x1a\x12\xcb\xd1\xb1d\xeb\xbd\x03/\x15sh\x09\xf0\
\xbd\x84xj\x16\x94\xeb\x8e\x15 L\xe0\xe0\xe7\x12$\
9PX\x18\x9c\xfd\xa8a`\x10\x16yr\xd8\x81$\
U\x12\xe8N\x10\xd0B\x0c\x1c\x04\x02\x1e\xb0e\x83\xee\
\x81\xc2\x98$ \x0c\x03\x08\x8b<\x83\x05n\xf4%\xac\
A!.P9$%\x18x\x91\xc6\x0d\xc2\x10Vi\
'~\x0f\x15\xbe\xcf\xe9U\xd8)' \xb9r\x9d\xa9\
\xddNs\x8c\x9b\x8d\xad\x0d\x0b\xcc\xd7:T\xcb$0\
\x8fAm\xc3\x7f\xfc\x93i\xf3\xba_\x02\x0c\x00*i\
\xfdt\x91f0\xd9\x00\x00\x00\x00IEND\xaeB\
`\x82\
\x00\x00\x02\x09\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x01\xabIDATx\xda\xa4\
S\xbdJ\x03A\x10\xfe\xf6\xa2\x89\xbf\xa5X(\xa8\xad\
\xd8\x9d\xa5(>@|\x03\x05+{\x9f\xc0\xc6\x87\x08\
X\x08\xfa\x04\x82\xa0\x82 \xda&\x01\x1b\x8bT\x8a\xa0\
`\xa1F\xe5\xfe\xb3\xeb\xccN\xce\xbb\xf3b\xe5\xc0\xdc\
\xfe\xcc\xcc\xb7\xdf7\xbb\xa7\x8c1\xf8\x8f\x0d\xf1\xc7\xdd\
[A{\xb1S\x8c(48\xd4_\xb5`\xb0S\x88\
\x1f\x7f\xc1\x9cx\x02\xd0\x9e\xb9\x03<\x05\x8c:Y\x82\
\x81\xbb\xbd\xbe\xe52\xc3\xc3\xab\xa3\x22\xb8\xaf\x81\xfaH\
\xc6\xc0ZBR<\x0a\xd42\x10\xdf\xf7e\xd2\xcb\x15\
\x87\x94\xd33E\x09?\xc6\x81\x80\x12\x86\x95\xe4\x86!\
l\x8f\xd2\x82\x98Fm\xca=(\x18'D4\x12\x91\
 \x08\x04\x80\xd9i\x96e\x067\x91\xac\xc1\x9a3\xfd\
R\x10E\x11\xb4\xd6\x02\x004\x7f\xd5\xb6\xc8w\x86\xfa\
\xfa\xdd\xfa\xda\x86\xcb\x9a\xf9\xc4\xd4\xd3\xf5\xea\xd2\xaa\x9b\
\xdfw\x1c\x077\xb7\xd79\x06tB\x1c\xc7\xd6\x85\x80\
\xac\xbb\x1f]x\x9e'\xf2\x93\x18FKq\xb5ZM\
Y\xfd\x00\xb4\xce.OK\xfaf\x17\xe6\x5c]\xd1x\
zxl\x95\x82\xcaJ\xe8\x03\xf0#\xc9\xf7\xc7\xd8f\
6\x93\x91$\xeb\x81\xa3\x96\xa1\xf0g\x13s\xb7@\x1e\
i!6\x96\x10\x96\x91\xeb\xe3\xb1\xea\xd8\xdb)\x03\x5c\
\x84\xc0y(;\xf3\x15`\xb3&X\x93Z\xae1\x14\
@\x1c\xd0\xc3\xba\xcf\xbd\xaa\xdd\x14/-f\xe3\x84\xc3\
\x00x\xa7\xd3\xc7\xa9x\x02vn\xf7\xf2\xc5\x03$\xf0\
|\x9a|\x0a\x8f\xba\x82N\xd2y\xdb\xbf\x97\xc8\x0b:\
\xb4\xe7\xf6\x05\xbe\x92?C\x9e\x1b\x14ST\xcav\x87\
\xd9\xf0yc()-\xb4\x97\x7f\x90O\xfb\xf0\xa9\xf6\
[\x80\x01\x00P*\xe7\x94\xb4>\xc3\x84\x00\x00\x00\x00\
IEND\xaeB`\x82\
\x00\x00\x02{\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x02\x1dIDATx\xda\x94\
SMk\x13Q\x14=\x8f\x8c\x99\x99\x98\x14)\xa5b\
\xac\x8dUZt$b\x88\x1f(-$\x9b\xee\xc4?\
 X\xdcKA\x14\x5c\x05\x89\x1b\x85n\xfc\x07]\x95\
.\xbaq\xdbM\x10\xc4B\xac\xd8\x12:I\x9b\xc6\xda\
F\x03R\x95\xb6\x94\xc4\x8ff^\xef}3I3\xd8\
E{\xe0\xce}\xf3\xee=g\xce\xbb3\x83\xaa\x10\xf8\
J1CA\xb8E\xf1\x803\xdf\x7f\xf3\xe2\xb0\x1as\
\x98+\xd4\x05\xc8R\xc1z#\xe5\xf9\xb4m's\x96\
\xf5\xf1\x9e\x10_\xd0\x81Cj\xb6\x042\xda_\xb7~\
\xedB6{75;\x8br\xb9\x8c\xd4\xc8H\xf2l\
:\x9d\xec\x14H\xe5r\x07\xb5\xd1\xd1\xe4\xe7L&\xc8\
\xfb\xdao\xb7\xae;\xbb\xbb\xf8\xb1\xb3\x83\xe2\xd8\x18N\
\xf5\xf7cok\xab\x93\xef\xabq/\xf1t%P\x97\
\x92\xb3\xe9\xd4\xeb\xb83=\x8d+\xf9<N\xe8:~\
\xf6\xf5!29\xd9\x16\xb8\x1a\x8f\xe3b,\x86\x93]\
]\xe0^\xe2\x99>\x07M\xda\xac\x8d\x8f\xc3\x0c\x85\xf0\
\x8f6\x1ato\xf4\xf4\xb4\x05x\x8c\xe1p\x18p\x1c\
po\xcb\x01.\xb9\xf1X\x1e\x13\xcca\xae&\xdd\x07\
\x04\x1c:\xca\xc3\x17\xdf\xb1\xbc\x5c\xc4\xe6\xe66\xba\xbb\
\x07\xf1\xfay\x00\xcdf\x93\x1e\xea\xa8 \x9e\xca\xe9D\
\x02\xc4\x0b\xa0S`\xcf\x9d\x05^>\xb9\xad\xf2\xd3W\
\x15\xdc\x18\x1aR\xeb\xf7KK\x18\xa6\x190\xde.,\
\x80{\xdb\x02h\x09\x902\xe3\xd9\xc4\x5c\xdb\xc1\x87\x95\
\x15\xe5 l\x9axW((\x07\x0c\xaf\xd7\xe7@c\
\xd5G\xf7\xeb\xb8|nXm\x14\xd6\xd6\x10\x1f\x18P\
\xeb\xf9R\x097-K\xad\xe7H\xc8s\xa0\xf9\x8f\xe0\
9\xb076\xd49\x8d`\x10\x8b\x95\x8ar\x102\x0c\
\xe4m\xdb\xe7@\xfe\xe7\xc0\x13\x18\x8cFU.U\xab\
\xb0\xe8\xbd3\x16WW\x91\xf0\xe61_,\xb6\x04\x5c\
\x07\x0d\xfa\x17\x0c\xfe\x0e<\xf5r\xad\xa6\x1c(7\xeb\
\xeb\xca\x01\xe3\x13\xcd\xa3\xe5\x80{\xa5\x10:\x7fCZ\
M\xcahL\x88H@\xd3p\xba\xb7\x17G\x01\xf7\x92\
l\x84\xb9l\xe3L\x03\xf8u\xdd0\xa6p\x0c\xfc!\
\x0es\xf7\x05\x18\x00\xb2\xf5<n'\xf1\x83[\x00\x00\
\x00\x00IEND\xaeB`\x82\
\x00\x00\x02s\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x02\x15IDATx\xda\x8c\
\x93\xb1k\x14A\x14\x87\x7f3\xbb{\x0b1\xa0A\x84\
\x10\x90\x08\x8aA\x0b\xaf\x11\xd2\x08W\x04\xb4\xb2\xd4\xc6\
\x88\x7f\x80ha\x95BQ[\xb1\x0a\x04,\xc4\x04B\
$Uj\x91X\x84TV\x9e\x06\x8c\x9a\x14\x1eGD\
L\xbc\xec\x9d\x97\xcb-{\xbb\xe3{on\xcc\xdeq\
\x85\x03ogvv\xde7\xef\x9baU\xe9\xf1*\x00\
u\x9d\x1ec\x18\xdc\xde\xc1\x98m\xc0\x00\x9f\x96d\xc2\
\x94\x17\xa5\xafD\x06>\xcf\xd3\xd4\xe9W\x0fJs\x87\
q\x82N\x9a!\xed\x18\xca1\xd8\xaa\xfc\xc2\x8b\xd5\xef\
wi\x81\xa6\xf8\x86\xe2mIT\x97\xa6\x81\x8f\x04Y\
[\xe4\x0fL0^\x9ctPo\xc5\x88\x9am\xec\xd5\
[\xf8\xf9\xbb\x89?\xcd\x16^\xcfL\xcd\xd1\xf7k\xb4\
h\xa2\xa7\xae\xe24\xce\xdc{\xc3\x15d\xfc\xea+\xad\
\xa0)\x82\xc0\xc3\xd7\x9d\x06\xf6\x1b1*;5|X\
X\xc3\xc5\xd1`v\xb3\xdaxo\x0a\xc7\xe6E\x09\xd8\
v\x1c\x07\x08\x14=<O\xc9d\xb3\x9d\xe0F\xe9\x1c\
\x0e\xe3\xf1\xbc\xd2$)M\xf6(\xc9\x80\x01&\x0b\x94\
R\xe0\x10\x08Y\xfd\xaf\x92\x03\x14(W\x14\x18B\xfb\
!\xaf\x14\x86\x9e\xf4\xbb\xb5\x08\x0b+\xeb\xb8z>\x9c\
U\x07\xbb\x0fUk/\xa7\xa08A\xce\x18Y\x96!\
\xaf\xc4\xc9\xdc.\x17'\x90$)FO\x0e\xe3\xed\x97\
\x83\x0d{\x06\x99\x00\x0a\x9ca!\xf6\x0a\x8f\x94\xd0\x03\
\x91-\xb4\x12\xed\xee!\xa6\x02\xe0\xbd\xb8d\xe6I\x05\
\xea\xe8\xbd\x1f\xe2yZ\xb4\xed\xf5Y@\xe8vd\x8d\
T\x00y\xa5^\x88o\x01R\x81./?\x01:\xed\
S>\xad\x1e\x0a\x02\x09m$\xef\x1f\xc4\x1d.\x9f\x89\
\x8c\x19\x9cQ\x05D\xf6\xa3\xea\xe6xT\xd9\xa8\xdd\x99\
y\xb9\xee\x1cM8|\xf6\xe6\xd4\x851\xa7\xa0\xbbD\
W\x89f?\x93Z\x05\x8azy\xe5\xf9#\xbb\xa7m\
W\xee\xcf?\xa3\x1do\xb9\x0a\xfa!\x9cO\xeaaT\
\xfd,\x80\xa8\x1b\xae\x9d \xc7!\xa7\xc47b\xff7\
\xd81\xf5\x05\xdf\x13\xed\xf2\xf2\xd3\x11\x7f\xc0\xef{\xbc\
_iP\x8b~l\xedS7\xf2W\x80\x01\x00\x154\
\x01\x9c\x9a\xa5\x0c\xb9\x00\x00\x00\x00IEND\xaeB\
`\x82\
"

qt_resource_name = b"\
\x00\x05\
\x00o\xa6S\
\x00i\
\x00c\x00o\x00n\x00s\
\x00\x08\
\x0e\xba\x14H\
\x00c\
\x00h\x00e\x00c\x00k\x00b\x00o\x00x\
\x00\x0b\
\x01d\x8d\x87\
\x00c\
\x00h\x00e\x00c\x00k\x00e\x00d\x00.\x00s\x00v\x00g\
\x00\x0d\
\x09$\x8d\xe7\
\x00u\
\x00n\x00c\x00h\x00e\x00c\x00k\x00e\x00d\x00.\x00s\x00v\x00g\
\x00\x0a\
\x0c\xad\x0f\x07\
\x00d\
\x00e\x00l\x00e\x00t\x00e\x00.\x00p\x00n\x00g\
\x00\x0a\
\x06\xcbO\xc7\
\x00r\
\x00e\x00m\x00o\x00v\x00e\x00.\x00p\x00n\x00g\
\x00\x0e\
\x0d\x0b\xd9\xe7\
\x00p\
\x00r\x00o\x00p\x00e\x00r\x00t\x00i\x00e\x00s\x00.\x00p\x00n\x00g\
\x00\x08\
\x08\xc8Xg\
\x00s\
\x00a\x00v\x00e\x00.\x00p\x00n\x00g\
\x00\x07\
\x07\xa7W\x87\
\x00a\
\x00d\x00d\x00.\x00p\x00n\x00g\
\x00\x13\
\x0d\x05~\xe7\
\x00c\
\x00a\x00l\x00e\x00n\x00d\x00a\x00r\x00-\x00s\x00e\x00l\x00e\x00c\x00t\x00.\x00p\
\x00n\x00g\
\x00\x08\
\x06|Z\x07\
\x00c\
\x00o\x00p\x00y\x00.\x00p\x00n\x00g\
"

qt_resource_struct = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x02\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x07\x00\x00\x00\x05\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x10\x00\x02\x00\x00\x00\x02\x00\x00\x00\x03\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00&\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x97R\x96\xfd\xa6\
\x00\x00\x00B\x00\x00\x00\x00\x00\x01\x00\x00\x04\xd1\
\x00\x00\x01\x97R\x96\xe7}\
\x00\x00\x01\x0e\x00\x00\x00\x00\x00\x01\x00\x00\x15\xb5\
\x00\x00\x01(\xb3hY\xc0\
\x00\x00\x00|\x00\x00\x00\x00\x00\x01\x00\x00\x0b*\
\x00\x00\x01'-\x88\x08\x10\
\x00\x00\x00\xce\x00\x00\x00\x00\x00\x01\x00\x00\x11)\
\x00\x00\x01!4\x0f\xb8P\
\x00\x00\x00\xb8\x00\x00\x00\x00\x00\x01\x00\x00\x0f\x12\
\x00\x00\x01&\x84\xe4\xe3\x90\
\x00\x00\x00b\x00\x00\x00\x00\x00\x01\x00\x00\x08\x8f\
\x00\x00\x01&9\xd6\xde\x80\
\x00\x00\x00\xe2\x00\x00\x00\x00\x00\x01\x00\x00\x136\
\x00\x00\x01\x96\xc1\xa78J\
\x00\x00\x00\x96\x00\x00\x00\x00\x00\x01\x00\x00\x0c\x0e\
\x00\x00\x01\x22S@\x03\xa0\
"

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
